<template>
  <div class="flex">
    <MainSidebar :subsidiary="subsidiary" />
    <div class="flex-1 p-8">
      <div
        class="bg-white shadow-lg rounded-lg p-8"

      >
        <transition name="fade">
          <div v-if="snackbar.show" class="success-message">
            <span class="text-white font-semibold">{{ snackbar.message }}</span>
          </div>
        </transition>

        <!-- Header -->
        <div class="flex items-center mb-6 border-b pb-4">
          <img :src="theme.logo" alt="Company Logo" class="h-24 mr-6" />
          <div class="flex-1 text-center">
            <h2
              class="text-3xl font-bold uppercase tracking-wide"
              :style="{ color: theme.primaryColor }"
            >
              {{ theme.name }}
            </h2>
            <h3 class="text-lg font-semibold text-gray-600">
              User Access Request Form
            </h3>
          </div>
        </div>

        <!-- Form -->
        <form @submit.prevent="submitForm">
          <!-- User Details -->
          <div v-if="subsidiary === 'premieruganda'">
            <!-- Premier Uganda specific fields -->
            <div class="grid grid-cols-2 gap-6">
              <TextField label="First Name" v-model="form.firstName" required />
              <TextField label="Middle Name" v-model="form.middleName" />
              <TextField label="Last Name" v-model="form.lastName" required />
              <TextField label="NIN" v-model="form.nin" required />
              <TextField
                label="Date of Birth"
                v-model="form.dateOfBirth"
                type="date"
                required
              />
              <PhoneNumberField
                ref="phoneField"
                label="Mobile Phone"
                v-model="form.telephone"
                :subsidiary="subsidiary"
                required
              />
            </div>

            <!-- Next of Kin Details -->
            <div class="mt-6">
              <h4 class="font-semibold mb-4 text-lg">Next of Kin Details</h4>
              <div class="grid grid-cols-2 gap-6">
                <TextField
                  label="Next of Kin – Full Name"
                  v-model="form.nextOfKinName"
                  required
                />
                <PhoneNumberField
                  label="Next of Kin – Mobile Number"
                  v-model="form.nextOfKinMobile"
                  :subsidiary="subsidiary"
                  required
                />
                <TextField
                  label="Next of Kin – Relationship"
                  v-model="form.nextOfKinRelationship"
                  required
                />
                <TextField
                  label="Next of Kin – Dependents"
                  v-model="form.nextOfKinDependents"
                  required
                />
              </div>
            </div>

            <!-- TIN and Email -->
            <div class="grid grid-cols-2 gap-6 mt-6">
              <TextField label="TIN" v-model="form.tin" required />
              <TextField
                label="Email Address"
                v-model="form.email"
                type="email"
              />
            </div>
          </div>

          <!-- Default fields for other subsidiaries -->
          <div v-else class="grid grid-cols-2 gap-6">
            <TextField label="First Name" v-model="form.firstName" />
            <TextField label="Last Name" v-model="form.lastName" />
            <TextField
              label="Email Address"
              v-model="form.email"
              type="email"
            />
            <PhoneNumberField
              ref="phoneField"
              label="Telephone No."
              v-model="form.telephone"
              :subsidiary="subsidiary"
            />
          </div>

          <!-- Branch, System Name, Department -->
          <div v-if="subsidiary === 'premieruganda'" class="grid grid-cols-2 gap-6 mt-6">
            <DropdownField
              label="Branch"
              v-model="form.branch"
              :options="branches"
            />
            <div>
              <label class="font-semibold block mb-2">System Name</label>
              <div class="relative border rounded p-2 bg-white">
                <div
                  class="cursor-pointer"
                  @click="showSystemDropdown = !showSystemDropdown"
                >
                  <span v-if="form.systemName.length">
                    {{
                      form.systemName.includes("ALL")
                        ? "All Systems"
                        : form.systemName.join(", ")
                    }}
                  </span>
                  <span v-else class="text-gray-400">Select system(s)</span>
                </div>
                <div
                  v-if="showSystemDropdown"
                  class="absolute z-10 bg-white border rounded mt-1 shadow max-h-48 overflow-y-auto w-full"
                >
                  <div class="p-2">
                    <label class="block">
                      <input
                        type="checkbox"
                        value="ALL"
                        v-model="form.systemName"
                        @change="toggleAllSystems"
                        class="mr-2"
                      />
                      All
                    </label>
                    <label
                      v-for="system in systemNames"
                      :key="system"
                      class="block mt-1"
                    >
                      <input
                        type="checkbox"
                        :value="system"
                        v-model="form.systemName"
                        class="mr-2"
                        :disabled="form.systemName.includes('ALL')"
                      />
                      {{ system }}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Default layout for other subsidiaries -->
          <div v-else class="grid grid-cols-2 gap-6 mt-6">
            <DropdownField
              label="Branch"
              v-model="form.branch"
              :options="branches"
            />
            <div>
              <label class="font-semibold block mb-2">System Name</label>
              <div class="relative border rounded p-2 bg-white">
                <div
                  class="cursor-pointer"
                  @click="showSystemDropdown = !showSystemDropdown"
                >
                  <span v-if="form.systemName.length">
                    {{
                      form.systemName.includes("ALL")
                        ? "All Systems"
                        : form.systemName.join(", ")
                    }}
                  </span>
                  <span v-else class="text-gray-400">Select system(s)</span>
                </div>
                <div
                  v-if="showSystemDropdown"
                  class="absolute z-10 bg-white border rounded mt-1 shadow max-h-48 overflow-y-auto w-full"
                >
                  <div class="p-2">
                    <label class="block">
                      <input
                        type="checkbox"
                        value="ALL"
                        v-model="form.systemName"
                        @change="toggleAllSystems"
                        class="mr-2"
                      />
                      All
                    </label>
                    <label
                      v-for="system in systemNames"
                      :key="system"
                      class="block mt-1"
                    >
                      <input
                        type="checkbox"
                        :value="system"
                        v-model="form.systemName"
                        class="mr-2"
                        :disabled="form.systemName.includes('ALL')"
                      />
                      {{ system }}
                    </label>
                  </div>
                </div>
              </div>
            </div>
            <DropdownField
              label="Department"
              v-model="form.department"
              :options="departments"
            />
          </div>

          <!-- Nature of Access -->
          <div class="mt-6 p-6 border rounded-md shadow-sm bg-gray-100">
            <h4 class="font-semibold mb-2">Nature of Access Request</h4>
            <div class="grid grid-cols-2 gap-4">
              <RadioButton
                v-for="option in accessTypes"
                :key="option"
                v-model="form.accessType"
                :value="option"
                :label="option"
              />
            </div>
          </div>

          <!-- Role & Reason -->
          <div class="mt-6">
            <div v-if="subsidiary === 'premieruganda'" class="grid grid-cols-2 gap-6">
              <TextField
                label="Previous Role (if applicable)"
                v-model="form.previousRole"
              />
            </div>
            <div v-else class="grid grid-cols-2 gap-6">
              <DropdownField
                label="Role To Be Assigned"
                v-model="form.role"
                :options="roles"
              />
              <TextField
                label="Previous Role (if applicable)"
                v-model="form.previousRole"
              />
            </div>
            <div class="mt-6">
              <TextareaField
                label="Reason for Accessing System(s)"
                v-model="form.reason"
              />
            </div>
          </div>

          <!-- HR Section for Premier Uganda -->
          <div v-if="subsidiary === 'premieruganda'" class="mt-8 p-6 border rounded-md shadow-sm bg-blue-50">
            <h4 class="font-semibold mb-4 text-lg text-blue-800">Additional Information to Be Completed by HR</h4>
            <div class="grid grid-cols-2 gap-6">
              <TextField
                label="Staff ID Number"
                v-model="form.staffId"
                placeholder="Filled in by HR before sending to MIS"
                required
              />
              <TextField
                label="Position"
                v-model="form.position"
                placeholder="Free text field filled by HR"
                required
              />
              <DropdownField
                label="Department"
                v-model="form.department"
                :options="departments"
                required
              />
              <TextField
                label="Start Date"
                v-model="form.startDate"
                type="date"
                placeholder="Date when staff is expected to begin"
                required
              />
            </div>
          </div>

          <!-- File Uploads -->
          <div class="mt-8">
            <h4 class="font-semibold mb-4">Supporting Documents</h4>
            <div class="grid grid-cols-2 gap-6">
              <div v-for="(label, key) in fileFields" :key="key">
                <label class="block font-semibold mb-1">{{ label }}</label>

                <!-- Special handling for Other Documents (multiple files) -->
                <div v-if="key === 'otherDocuments'">
                  <div
                    class="relative border-2 border-dashed rounded-md p-4 cursor-pointer hover:border-blue-500 bg-gray-50"
                    @click="fileInputRefs[key]?.click()"
                  >
                    <input
                      type="file"
                      :ref="(el) => setFileRef(el, key)"
                      class="hidden"
                      multiple
                      @change="(e) => handleFileChange(e, key)"
                    />
                    <div class="text-center">
                      <span class="text-sm text-gray-700">
                        {{ files[key].length > 0 ? `${files[key].length} file(s) selected` : "Click to select multiple files" }}
                      </span>
                      <p class="text-xs text-gray-500 mt-1">
                        Non Disclosure form, 20% Savings Consent, Application letter, Fraud Addendum, Non disclosure Agreement form, LC1 letter, ISA, Training Evaluation form, Interview Assessment form, Academic Documents (S.6 minimum)
                      </p>
                    </div>
                  </div>

                  <!-- Display selected files -->
                  <div v-if="files[key].length > 0" class="mt-2 space-y-1">
                    <div v-for="(file, index) in files[key]" :key="index" class="flex items-center justify-between bg-gray-100 p-2 rounded text-sm">
                      <span class="truncate">{{ file.name }}</span>
                      <button
                        class="text-red-600 hover:text-red-800 ml-2"
                        @click.stop.prevent="removeFileFromArray(key, index)"
                        title="Remove File"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Regular single file upload for other fields -->
                <div v-else
                  class="relative border-2 border-dashed rounded-md p-4 cursor-pointer hover:border-blue-500 bg-gray-50 flex justify-between items-center"
                  @click="fileInputRefs[key]?.click()"
                >
                  <input
                    type="file"
                    :ref="(el) => setFileRef(el, key)"
                    class="hidden"
                    @change="(e) => handleFileChange(e, key)"
                  />
                  <span class="truncate text-sm text-gray-700">
                    {{ files[key]?.name || "Click to select file" }}
                  </span>
                  <button
                    v-if="files[key]"
                    class="text-red-600 hover:text-red-800 text-base font-bold ml-4"
                    @click.stop.prevent="removeFile(key)"
                    title="Remove File"
                  >
                    ×
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Bulk Upload Section for Premier Uganda -->
          <div v-if="subsidiary === 'premieruganda'" class="mt-8">
            <div class="flex items-center justify-between mb-4">
              <h4 class="font-semibold text-lg">Bulk User Upload</h4>
              <button
                type="button"
                @click="showBulkUpload = !showBulkUpload"
                class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition"
              >
                {{ showBulkUpload ? 'Hide Bulk Upload' : 'Show Bulk Upload' }}
              </button>
            </div>

            <div v-if="showBulkUpload" class="p-6 border rounded-md shadow-sm bg-purple-50">
              <div class="mb-4">
                <p class="text-sm text-gray-700 mb-2">
                  Upload an Excel file (.xlsx or .xls) with user information. The file should have the following columns:
                </p>
                <div class="bg-gray-100 p-3 rounded text-xs font-mono mb-4">
                  FirstName,MiddleName,LastName,NIN,DateOfBirth,MobilePhone,NextOfKinName,NextOfKinMobile,NextOfKinRelationship,NextOfKinDependents,TIN,Email,Branch,SystemName,AccessType,Reason,StaffId,Position,Department,StartDate
                </div>
              </div>

              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Upload Excel File (.xlsx or .xls)
                </label>
                <div class="flex items-center space-x-4">
                  <div
                    class="relative border-2 border-dashed border-purple-300 rounded-md p-4 cursor-pointer hover:border-purple-500 bg-white flex-1"
                    @click="$refs.bulkFileInput?.click()"
                  >
                    <input
                      ref="bulkFileInput"
                      type="file"
                      class="hidden"
                      accept=".xlsx,.xls"
                      @change="handleBulkFileUpload"
                    />
                    <div class="text-center">
                      <i class="fas fa-cloud-upload-alt text-purple-500 text-2xl mb-2"></i>
                      <p class="text-sm text-gray-700">
                        {{ bulkFile ? bulkFile.name : 'Click to select file' }}
                      </p>
                    </div>
                  </div>

                  <button
                    type="button"
                    @click="downloadBulkTemplate"
                    class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Download Template
                  </button>
                </div>
              </div>

              <div v-if="bulkFile" class="mb-4">
                <button
                  type="button"
                  @click="processBulkUpload"
                  class="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition"
                  :disabled="isBulkUploading"
                >
                  {{ isBulkUploading ? 'Processing...' : 'Process Bulk Upload' }}
                </button>
              </div>

              <div v-if="bulkUploadResults.length > 0" class="mt-4">
                <h5 class="font-medium mb-2">Upload Results</h5>
                <div class="bg-white p-4 rounded-md max-h-60 overflow-y-auto">
                  <div v-for="(result, index) in bulkUploadResults" :key="index" class="mb-2 text-sm flex items-center">
                    <i :class="result.success ? 'fas fa-check-circle text-green-500 mr-2' : 'fas fa-times-circle text-red-500 mr-2'"></i>
                    <span :class="result.success ? 'text-green-600' : 'text-red-600'">
                      Row {{ result.row }}: {{ result.message }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Submit -->
          <div class="mt-10 text-center">
            <button
              type="submit"
              class="bg-blue-600 text-white px-8 py-3 w-full rounded-md hover:bg-blue-700 transition shadow-lg font-semibold text-lg"
              :style="{ backgroundColor: isSubmitting ? '#999999' : theme.primaryColor }"
              :disabled="isSubmitting"
            >
              {{ isSubmitting ? 'Submitting...' : 'Submit Request' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { subsidiaries } from "@/config/subsidiaries";
import TextField from "@/components/TextField.vue";
import DropdownField from "@/components/DropdownField.vue";
import TextareaField from "@/components/TextareaField.vue";
import RadioButton from "@/components/RadioButton.vue";
import PhoneNumberField from "@/components/PhoneNumberField.vue";
import MainSidebar from "@/components/MainSidebar.vue";
import {
  submitAccessForm,
  getBranches,
  getRoles,
  getDepartments,
} from "@/services/apiService";

export default {
  name: "RequestForm",
  components: {
    TextField,
    DropdownField,
    TextareaField,
    RadioButton,
    PhoneNumberField,
    MainSidebar,
  },
  props: ["subsidiary"],
  data() {
    return {
      theme: subsidiaries[this.subsidiary] || subsidiaries['platinumkenya'],
      form: {
        systemName: [],
        branch: "",
        firstName: "",
        middleName: "",
        lastName: "",
        nin: "",
        dateOfBirth: "",
        nextOfKinName: "",
        nextOfKinMobile: "",
        nextOfKinRelationship: "",
        nextOfKinDependents: "",
        tin: "",
        email: "",
        telephone: "",
        department: "",
        accessType: "",
        role: "",
        previousRole: "",
        reason: "",
        // HR fields for Premier Uganda
        staffId: "",
        position: "",
        startDate: "",
      },
      isSubmitting: false,
      showSystemDropdown: false,
      systemNames: [],
      departments: [],
      accessTypes: [
        "New Access",
        "Additional Access",
        "Modify Existing Access",
        "Disable Access (Access no longer required)",
        "Intranet Access",
      ],
      branches: [],
      roles: [],
      snackbar: {
        show: false,
        message: "",
      },
      files: {
        cv: null,
        id: null,
        kraPin: null,
        nssf: null,
        sha: null,
        bankDetails: null,
        passportImage: null,
        contract: null,
        localGovId: null,
        refereesLetter: null,
        personalInfoForm: null,
        passportSizes: null,
        applicationLetter: null,
        tin: null,
        // Additional field for Platinum Uganda
        otherDocuments: [],
      },
      fileFields: {},
      fileInputRefs: {},
      // Bulk upload properties
      showBulkUpload: false,
      bulkFile: null,
      isBulkUploading: false,
      bulkUploadResults: [],
    };
  },
  methods: {
    // Bulk upload methods
    handleBulkFileUpload(event) {
      const file = event.target.files[0];
      if (file) {
        // Validate file type
        const allowedTypes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
          'application/vnd.ms-excel' // .xls
        ];

        if (!allowedTypes.includes(file.type)) {
          this.snackbar.message = 'Please select a valid Excel file (.xlsx or .xls)';
          this.snackbar.show = true;
          setTimeout(() => (this.snackbar.show = false), 3000);
          event.target.value = '';
          return;
        }

        // Validate file size (10MB max)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
          this.snackbar.message = 'File size must be less than 10MB';
          this.snackbar.show = true;
          setTimeout(() => (this.snackbar.show = false), 3000);
          event.target.value = '';
          return;
        }

        this.bulkFile = file;
        this.bulkUploadResults = [];
      }
    },

    downloadBulkTemplate() {
      // Create Excel template content for Premier Uganda
      const csvContent = `FirstName,MiddleName,LastName,NIN,DateOfBirth,MobilePhone,NextOfKinName,NextOfKinMobile,NextOfKinRelationship,NextOfKinDependents,TIN,Email,Branch,SystemName,AccessType,Reason,StaffId,Position,Department,StartDate
John,Michael,Doe,CM12345678901234,1990-01-15,+256701234567,Jane Doe,+256701234568,Sister,2,1234567890,<EMAIL>,Head Office,Mambu,New Access,New employee access,EMP001,Software Developer,IT,2024-01-20`;

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'premier_uganda_bulk_upload_template.csv';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.snackbar.message = 'Template downloaded successfully!';
      this.snackbar.show = true;
      setTimeout(() => (this.snackbar.show = false), 3000);
    },

    async processBulkUpload() {
      if (!this.bulkFile) return;

      this.isBulkUploading = true;
      this.bulkUploadResults = [];

      try {
        const formData = new FormData();
        formData.append('file', this.bulkFile);
        formData.append('subsidiary', this.subsidiary);

        const response = await fetch(`/api/access-requests/bulk-upload/${this.subsidiary}`, {
          method: 'POST',
          body: formData,
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          }
        });

        const result = await response.json();

        if (response.ok) {
          this.bulkUploadResults = result.results || [];
          this.snackbar.message = `Bulk upload completed! ${result.successCount || 0} successful, ${result.errorCount || 0} errors.`;
          this.snackbar.show = true;
          setTimeout(() => (this.snackbar.show = false), 5000);
        } else {
          throw new Error(result.error || 'Bulk upload failed');
        }
      } catch (error) {
        console.error('Bulk upload error:', error);
        this.snackbar.message = `Bulk upload failed: ${error.message}`;
        this.snackbar.show = true;
        setTimeout(() => (this.snackbar.show = false), 5000);
      } finally {
        this.isBulkUploading = false;
      }
    },

    async submitForm() {
      // If already submitting, prevent duplicate submissions
      if (this.isSubmitting) {
        console.log('Form submission already in progress');
        return;
      }

      // Check for phone number validation errors
      const phoneField = this.$refs.phoneField;
      if (phoneField && phoneField.error) {
        console.log('Form submission blocked due to phone number validation error:', phoneField.error);
        this.snackbar.message = "Please fix the phone number error before submitting.";
        this.snackbar.show = true;
        setTimeout(() => (this.snackbar.show = false), 3000);
        return;
      }

      // Set submitting flag to true
      this.isSubmitting = true;

      try {
        const formData = new FormData();
        Object.entries(this.form).forEach(([key, value]) => {
          formData.append(key, Array.isArray(value) ? value.join(", ") : value);
        });
        Object.entries(this.files).forEach(([key, file]) => {
          if (key === 'otherDocuments' && Array.isArray(file)) {
            // Handle multiple files for Other Documents
            file.forEach(f => {
              if (f) formData.append(key, f);
            });
          } else if (file) {
            // Handle single file for other fields
            formData.append(key, file);
          }
        });

        await submitAccessForm(formData, this.subsidiary);

        this.snackbar.message = "Request Submitted Successfully!";
        this.snackbar.show = true;
        setTimeout(() => (this.snackbar.show = false), 3000);
        this.resetForm();
      } catch (error) {
        console.error("Error submitting form:", error);

        // Check if the error is related to phone number duplication
        if (error.response?.data?.message?.includes('phone number') ||
            error.response?.data?.error?.includes('Phone number already exists')) {
          this.snackbar.message = error.response.data.message || "Phone number already exists in the system.";
        } else {
          this.snackbar.message = "Submission Failed!";
        }

        this.snackbar.show = true;
        setTimeout(() => (this.snackbar.show = false), 5000);
      } finally {
        // Always reset the submitting flag when done
        this.isSubmitting = false;
      }
    },
    toggleAllSystems() {
      this.form.systemName = this.form.systemName.includes("ALL")
        ? ["ALL"]
        : [];
    },
    handleFileChange(e, key) {
      if (key === 'otherDocuments') {
        // Handle multiple files for Other Documents
        const files = Array.from(e.target.files);
        this.files[key] = [...this.files[key], ...files];
      } else {
        // Handle single file for other fields
        const file = e.target.files[0];
        if (file) this.files[key] = file;
      }
    },
    removeFile(key) {
      if (key === 'otherDocuments') {
        this.files[key] = [];
      } else {
        this.files[key] = null;
      }
    },
    removeFileFromArray(key, index) {
      this.files[key].splice(index, 1);
    },
    setFileRef(el, key) {
      if (el) this.fileInputRefs[key] = el;
    },
    async fetchBranches() {
      try {
        console.log('Fetching branches for subsidiary:', this.subsidiary);
        const response = await getBranches(this.subsidiary);
        this.branches = response.map((b) => b.value);
        console.log('Branches fetched:', this.branches);
      } catch (error) {
        console.error('Error fetching branches:', error);
        this.branches = [];
      }
    },
    async fetchRoles() {
      try {
        console.log('Fetching roles for subsidiary:', this.subsidiary);
        const response = await getRoles(this.subsidiary);
        this.roles = response.map((r) => r.value);
        console.log('Roles fetched:', this.roles);
      } catch (error) {
        console.error('Error fetching roles:', error);
        this.roles = [];
      }
    },
    async fetchDepartments() {
      try {
        console.log('Fetching departments for subsidiary:', this.subsidiary);
        const response = await getDepartments(this.subsidiary);
        this.departments = response.map((d) => d.value);
        console.log('Departments fetched:', this.departments);
      } catch (error) {
        console.error('Error fetching departments:', error);
        this.departments = [];
      }
    },
    resetForm() {
      this.form = {
        systemName: [],
        branch: "",
        firstName: "",
        middleName: "",
        lastName: "",
        nin: "",
        dateOfBirth: "",
        nextOfKinName: "",
        nextOfKinMobile: "",
        nextOfKinRelationship: "",
        nextOfKinDependents: "",
        tin: "",
        email: "",
        telephone: "",
        department: "",
        accessType: "",
        role: "",
        previousRole: "",
        reason: "",
        // HR fields for Premier Uganda
        staffId: "",
        position: "",
        startDate: "",
      };
      Object.keys(this.files).forEach((key) => {
        if (key === 'otherDocuments') {
          this.files[key] = [];
        } else {
          this.files[key] = null;
        }
      });
      // Ensure the submitting flag is reset
      this.isSubmitting = false;
    },

    setSystemNamesBySubsidiary() {
      console.log('Setting system names for subsidiary:', this.subsidiary);

      try {
        // Default system names for most subsidiaries
        const defaultSystemNames = ["Mambu", "Email", "Akili", "Juakali", "Yeastar"];

        // Premier Uganda specific system names
        const premierUgandaSystemNames = [
          "Mambu",
          "CRM",
          "Yeastar",
          "Smart Collect",
          "Juakali",
          "PCA / HCM",
          "Pepea",
          "D365",
          "PLA"
        ];

        // Set system names based on subsidiary
        switch (this.subsidiary) {
          case 'premieruganda':
            console.log('Using Premier Uganda system names');
            this.systemNames = premierUgandaSystemNames;
            break;
          default:
            console.log('Using default system names');
            this.systemNames = defaultSystemNames;
            break;
        }

        console.log('System names set successfully:', this.systemNames);
      } catch (error) {
        console.error('Error setting system names:', error);
        // Set default system names as fallback
        this.systemNames = ["Mambu", "Email", "Akili", "Juakali", "Yeastar"];
      }
    },

    setFileFieldsBySubsidiary() {
      console.log('Setting file fields for subsidiary:', this.subsidiary);

      try {
        // Default file fields for Kenyan subsidiaries (platinumkenya, premierkenya, momentumcredit)
        const kenyaFileFields = {
          cv: "Upload CV",
          id: "Upload ID",
          kraPin: "Upload KRA PIN",
          nssf: "Upload NSSF",
          sha: "Upload SHA",
          bankDetails: "Upload Bank Details",
          passportImage: "Upload Passport Image",
        };

        // File fields for Spectrum Zambia
        const spectrumZambiaFileFields = {
          cv: "Upload CV",
          id: "Upload ID",
          kraPin: "TPIN Certificate",
          bankDetails: "Upload Bank Details",
          passportImage: "Upload Passport/ NRC Number Image",
        };

        // File fields for Platinum Tanzania and Premier Fanikiwa
        const tanzaniaFileFields = {
          cv: "Upload CV",
          contract: "Upload Contract",
          localGovId: "Upload Local Government identification Letter",
          refereesLetter: "Upload Referees Letter",
          personalInfoForm: "Upload Personal Information Form",
          passportSizes: "Upload Passport Sizes",
          applicationLetter: "Upload Application Letter",
          tin: "Upload TIN (Tax Identification Number)",
        };

        // File fields for Platinum Uganda
        const platinumUgandaFileFields = {
          cv: "Upload CV",
          id: "Upload National ID",
          tin: "Upload TIN certificate/Email from URA",
          personalInfoForm: "Upload Biodata form with Passport photo",
          applicationLetter: "Upload System Access form",
          otherDocuments: "Other Documents",
        };

        // File fields for Premier Uganda
        const premierUgandaFileFields = {
          cv: "Upload CV",
          id: "Upload National ID",
          tin: "Upload TIN certificate/Email from URA",
          personalInfoForm: "Upload Biodata form with Passport photo",
          applicationLetter: "Upload System Access form",
          otherDocuments: "Other Documents",
        };

        // Set file fields based on subsidiary
        switch (this.subsidiary) {
          case 'spectrumzambia':
            console.log('Using Spectrum Zambia file fields');
            this.fileFields = spectrumZambiaFileFields;
            break;
          case 'platinumtanzania':
          case 'premierfanikiwa':
            console.log('Using Tanzania file fields');
            this.fileFields = tanzaniaFileFields;
            break;
          case 'platinumuganda':
            console.log('Using Platinum Uganda file fields');
            this.fileFields = platinumUgandaFileFields;
            break;
          case 'premieruganda':
            console.log('Using Premier Uganda file fields');
            this.fileFields = premierUgandaFileFields;
            break;
          default:
            console.log('Using Kenya file fields (default)');
            this.fileFields = kenyaFileFields;
            break;
        }

        console.log('File fields set successfully:', this.fileFields);
      } catch (error) {
        console.error('Error setting file fields:', error);
        // Set default file fields as fallback
        this.fileFields = {
          cv: "Upload CV",
          id: "Upload ID",
        };
      }
    },
  },
  mounted() {
    console.log('RequestForm mounted - Subsidiary:', this.subsidiary);

    // Check if subsidiary is valid
    if (!this.subsidiary) {
      console.error('No subsidiary provided to RequestForm component');
      // Get user subsidiary from localStorage as fallback
      const user = JSON.parse(localStorage.getItem('user'));
      const userSubsidiary = user?.sub;
      if (userSubsidiary) {
        console.log(`Using user subsidiary ${userSubsidiary} as fallback`);
        this.$router.replace(`/request/${userSubsidiary}`);
        return;
      } else {
        console.log('No user subsidiary found, using platinumkenya as default');
        this.$router.replace('/request/platinumkenya');
        return;
      }
    }

    console.log('RequestForm mounted - Theme:', this.theme);

    // Fetch data for the form
    this.fetchBranches();
    this.fetchRoles();
    this.fetchDepartments();
    this.setSystemNamesBySubsidiary();
    this.setFileFieldsBySubsidiary();

    console.log('RequestForm mounted - File fields set:', this.fileFields);
  },
  watch: {
    subsidiary(newValue, oldValue) {
      console.log(`Subsidiary changed from ${oldValue} to ${newValue}`);
      // Update system names and file fields when subsidiary changes
      this.setSystemNamesBySubsidiary();
      this.setFileFieldsBySubsidiary();

      // Refresh data when subsidiary changes
      this.fetchBranches();
      this.fetchRoles();
      this.fetchDepartments();
    }
  },
};
</script>

<style scoped>
.success-message {
  background-color: #28a745;
  color: white;
  padding: 12px;
  text-align: center;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  transition: opacity 0.5s ease-in-out;
}
</style>
